# Durian Farms Generator

This Python script generates realistic data for 200 durian farms in Thailand with comprehensive farm information including names, addresses, e-trace adoption, and owner details.

## Generated Data Structure

### CSV Output: `durian_farms.csv`

| Column | Type | Description | Example |
|--------|------|-------------|---------|
| `id` | UUID | Unique farm identifier | `b68fedf4-a844-4ace-bdf8-9cce948059f0` |
| `name` | Text (Thai) | Farm name in Thai | `สวนผลไม้ทองคำ` |
| `address` | Text (Thai) | Complete Thai address | `เลขที่ 751 หมู่ที่ 4, ตำบลดอนตาล, จังหวัดระยอง` |
| `uses_etrace` | Boolean | E-trace system adoption | `True` or `False` |
| `owner` | Text (Thai) | Farm owner name in Thai | `สุนทร พูลสวัสดิ์` |

## Data Statistics

### Overall Summary
- **Total Farms**: 200
- **E-trace Adoption**: 141 farms (70.5%)
- **Non E-trace**: 59 farms (29.5%)

### Geographic Distribution
**Top 5 Provinces by Farm Count:**
1. **ภูเก็ต**: 21 farms (10.5%)
2. **สุราษฎร์ธานี**: 18 farms (9.0%)
3. **ชุมพร**: 17 farms (8.5%)
4. **นราธิวาส**: 16 farms (8.0%)
5. **กาญจนบุรี**: 16 farms (8.0%)

### Farm Name Analysis
**Farm Name Prefixes:**
- **สวน** (Garden): 99 farms (49.5%)
- **ไร่** (Plantation): 59 farms (29.5%)
- **เกษตรกรรม** (Agriculture): 22 farms (11.0%)
- **ฟาร์ม** (Farm): 20 farms (10.0%)

## Sample Records

```csv
id,name,address,uses_etrace,owner
b68fedf4-a844-4ace-bdf8-9cce948059f0,สวนผลไม้ทองคำ,"เลขที่ 751 หมู่ที่ 4, ตำบลดอนตาล, จังหวัดระยอง",False,สุนทร พูลสวัสดิ์
1e70c7f8-cb56-46db-b2ec-086a0d5e4c7f,ไร่เกษตรป่าไผ่,"เลขที่ 726 หมู่ที่ 11, ตำบลบ้านโป่ง, จังหวัดนครศรีธรรมราช",True,สมชาย สวัสดิ์ชัย
6e4fb0cb-4c95-4c4f-9c22-d36b65dd8d83,สวนทุเรียนบ้านสวน,"เลขที่ 816 หมู่ที่ 4, ตำบลคลองลาน, จังหวัดนราธิวาส",True,สมศรี ถาวรวงศ์
```

## Data Generation Features

### 1. **Realistic Thai Names**
- **Farm Names**: Combination of prefixes (สวน, ไร่, ฟาร์ม, etc.) and suffixes (ทองคำ, สุขใส, etc.)
- **Owner Names**: Authentic Thai first and last names
- **Geographic Names**: Real Thai provinces and tambons known for durian cultivation

### 2. **Authentic Addresses**
- **Format**: `เลขที่ [number] หมู่ที่ [village], ตำบล[tambon], จังหวัด[province]`
- **House Numbers**: Random 1-999
- **Village Numbers**: Random 1-15 (หมู่ที่)
- **Provinces**: 15 provinces known for durian farming

### 3. **E-trace Adoption Modeling**
- **70% adoption rate** - realistic for modern agricultural technology
- **Random distribution** across farms
- **Boolean values** for easy analysis

### 4. **Data Quality**
- **UTF-8 encoding** for proper Thai character display
- **UUID identifiers** for unique farm identification
- **Consistent formatting** across all records
- **No duplicate names or addresses**

## Technical Implementation

### Key Components

```python
# Farm name generation
farm_name_prefixes = ["สวน", "ไร่", "ฟาร์ม", "สวนผลไม้", "ไร่ทุเรียน", ...]
farm_name_suffixes = ["ทองคำ", "สุขใส", "เจริญรุ่งเรือง", ...]

# Address generation
provinces = ["ชุมพร", "ระยอง", "ตราด", "กาญจนบุรี", ...]
tambons = ["บ้านใหม่", "ท่าช้าง", "มะขาม", ...]

# E-trace adoption (70% probability)
def generate_uses_etrace():
    return random.random() < 0.7
```

### Generation Process
1. **UUID Generation**: Unique identifier for each farm
2. **Name Combination**: Random prefix + suffix selection
3. **Address Assembly**: House number + village + tambon + province
4. **E-trace Decision**: 70% probability of adoption
5. **Owner Assignment**: Random Thai first + last name combination

## Usage Examples

### Python Analysis
```python
import csv
import pandas as pd

# Load farm data
df = pd.read_csv('durian_farms.csv')

# E-trace adoption analysis
etrace_stats = df['uses_etrace'].value_counts()
print(f"E-trace adoption: {etrace_stats[True]/len(df)*100:.1f}%")

# Province distribution
provinces = df['address'].str.extract(r'จังหวัด(.+)')[0]
province_counts = provinces.value_counts()
print("Top provinces:", province_counts.head())

# Farm type analysis
farm_types = df['name'].str.extract(r'^(สวน|ไร่|ฟาร์ม|เกษตรกรรม)')[0]
type_counts = farm_types.value_counts()
print("Farm types:", type_counts)
```

### SQL Analysis (if imported to database)
```sql
-- E-trace adoption by province
SELECT 
    SUBSTR(address, INSTR(address, 'จังหวัด') + 7) as province,
    COUNT(*) as total_farms,
    SUM(CASE WHEN uses_etrace = 'True' THEN 1 ELSE 0 END) as etrace_farms,
    ROUND(SUM(CASE WHEN uses_etrace = 'True' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 1) as adoption_rate
FROM farms
GROUP BY province
ORDER BY total_farms DESC;

-- Farm size distribution by name prefix
SELECT 
    CASE 
        WHEN name LIKE 'สวน%' THEN 'สวน (Garden)'
        WHEN name LIKE 'ไร่%' THEN 'ไร่ (Plantation)'
        WHEN name LIKE 'ฟาร์ม%' THEN 'ฟาร์ม (Farm)'
        WHEN name LIKE 'เกษตรกรรม%' THEN 'เกษตรกรรม (Agriculture)'
        ELSE 'Other'
    END as farm_type,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM farms), 1) as percentage
FROM farms
GROUP BY farm_type
ORDER BY count DESC;
```

## Files Generated

- **`durian_farms_generator.py`** - Main generation script
- **`durian_farms.csv`** - Generated farm data (200 records)
- **`DURIAN_FARMS_README.md`** - This documentation

## Integration Possibilities

This farm data can be integrated with:
- **Packing house data** (for supply chain analysis)
- **Export shipment data** (for traceability)
- **Geographic information systems** (for mapping)
- **Agricultural databases** (for sector analysis)
- **E-trace system databases** (for technology adoption studies)

The data provides a realistic foundation for agricultural technology analysis, supply chain management, and policy development in the Thai durian industry.
