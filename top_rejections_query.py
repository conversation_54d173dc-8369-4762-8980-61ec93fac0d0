import sqlite3
from datetime import datetime, timedelta

DATABASE_NAME = 'durian_export.db'

def connect_database():
    """Connect to the SQLite database"""
    try:
        conn = sqlite3.connect(DATABASE_NAME)
        return conn
    except sqlite3.Error as e:
        print(f"Error connecting to database: {e}")
        return None

def top_10_exporters_by_rejections():
    """
    Query: Top 10 exporters with highest number of export rejections 
    in the past 12 months, split by reject reasons
    """
    conn = connect_database()
    if not conn:
        return
    
    cursor = conn.cursor()
    
    # Calculate timestamp for 12 months ago
    twelve_months_ago = datetime.now() - timedelta(days=365)
    twelve_months_ago_timestamp = int(twelve_months_ago.timestamp())
    
    print("=== Top 10 Exporters by Rejection Count (Past 12 Months) ===")
    print(f"Analysis period: {twelve_months_ago.strftime('%Y-%m-%d')} to {datetime.now().strftime('%Y-%m-%d')}")
    print()
    
    # Main query: Top 10 exporters by total rejections with breakdown by reason
    query = """
    WITH exporter_rejections AS (
        SELECT 
            p.id as packing_house_id,
            p.name as packing_house_name,
            p.owner as owner_name,
            COUNT(*) as total_rejections,
            SUM(CASE WHEN s.reject_reason_en = 'Quality below standards' THEN 1 ELSE 0 END) as quality_issues,
            SUM(CASE WHEN s.reject_reason_en = 'Improper packaging' THEN 1 ELSE 0 END) as packaging_issues,
            SUM(CASE WHEN s.reject_reason_en = 'Contains harmful chemicals' THEN 1 ELSE 0 END) as chemical_issues,
            SUM(CASE WHEN s.reject_reason_en = 'Incomplete documentation' THEN 1 ELSE 0 END) as documentation_issues,
            SUM(CASE WHEN s.reject_reason_en = 'Failed inspection' THEN 1 ELSE 0 END) as inspection_failures
        FROM packing_houses p
        JOIN export_shipments s ON p.id = s.packing_house_id
        WHERE s.status = 'rejected' 
        AND s.timestamp >= ?
        GROUP BY p.id, p.name, p.owner
        ORDER BY total_rejections DESC
        LIMIT 10
    )
    SELECT 
        packing_house_name,
        owner_name,
        total_rejections,
        quality_issues,
        packaging_issues,
        chemical_issues,
        documentation_issues,
        inspection_failures
    FROM exporter_rejections
    """
    
    cursor.execute(query, (twelve_months_ago_timestamp,))
    results = cursor.fetchall()
    
    if not results:
        print("No rejection data found for the past 12 months.")
        conn.close()
        return
    
    # Display results in a formatted table
    print("Rank | Exporter (Owner) | Total | Quality | Packaging | Chemical | Documentation | Inspection")
    print("-" * 100)
    
    for i, (house_name, owner, total, quality, packaging, chemical, docs, inspection) in enumerate(results, 1):
        print(f"{i:>4} | {owner[:15]:<15} ({house_name[:20]:<20}) | {total:>5} | {quality:>7} | {packaging:>9} | {chemical:>8} | {docs:>13} | {inspection:>10}")
    
    print()
    
    # Additional analysis: Percentage breakdown for top 5
    print("=== Detailed Breakdown for Top 5 Exporters ===")
    for i, (house_name, owner, total, quality, packaging, chemical, docs, inspection) in enumerate(results[:5], 1):
        print(f"\n{i}. {owner} ({house_name})")
        print(f"   Total Rejections: {total}")
        print(f"   - Quality below standards: {quality} ({quality/total*100:.1f}%)")
        print(f"   - Improper packaging: {packaging} ({packaging/total*100:.1f}%)")
        print(f"   - Contains harmful chemicals: {chemical} ({chemical/total*100:.1f}%)")
        print(f"   - Incomplete documentation: {docs} ({docs/total*100:.1f}%)")
        print(f"   - Failed inspection: {inspection} ({inspection/total*100:.1f}%)")
    
    conn.close()

def rejection_reasons_summary():
    """
    Additional query: Overall rejection reasons summary for context
    """
    conn = connect_database()
    if not conn:
        return
    
    cursor = conn.cursor()
    
    # Calculate timestamp for 12 months ago
    twelve_months_ago = datetime.now() - timedelta(days=365)
    twelve_months_ago_timestamp = int(twelve_months_ago.timestamp())
    
    print("\n=== Overall Rejection Reasons Summary (Past 12 Months) ===")
    
    query = """
    SELECT 
        reject_reason_en,
        reject_reason_th,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / (
            SELECT COUNT(*) 
            FROM export_shipments 
            WHERE status = 'rejected' AND timestamp >= ?
        ), 2) as percentage
    FROM export_shipments
    WHERE status = 'rejected' AND timestamp >= ?
    GROUP BY reject_reason_en, reject_reason_th
    ORDER BY count DESC
    """
    
    cursor.execute(query, (twelve_months_ago_timestamp, twelve_months_ago_timestamp))
    results = cursor.fetchall()
    
    print("Rejection Reason (English) | Thai | Count | Percentage")
    print("-" * 70)
    for reason_en, reason_th, count, percentage in results:
        print(f"{reason_en:<30} | {reason_th:<25} | {count:>5} | {percentage:>6}%")
    
    conn.close()

def monthly_rejection_trends():
    """
    Additional query: Monthly rejection trends for top exporters
    """
    conn = connect_database()
    if not conn:
        return
    
    cursor = conn.cursor()
    
    # Calculate timestamp for 12 months ago
    twelve_months_ago = datetime.now() - timedelta(days=365)
    twelve_months_ago_timestamp = int(twelve_months_ago.timestamp())
    
    print("\n=== Monthly Rejection Trends (Past 12 Months) ===")
    
    query = """
    SELECT 
        strftime('%Y-%m', datetime(timestamp, 'unixepoch')) as month,
        COUNT(*) as total_rejections,
        COUNT(DISTINCT packing_house_id) as unique_exporters
    FROM export_shipments
    WHERE status = 'rejected' AND timestamp >= ?
    GROUP BY month
    ORDER BY month
    """
    
    cursor.execute(query, (twelve_months_ago_timestamp,))
    results = cursor.fetchall()
    
    print("Month    | Total Rejections | Unique Exporters")
    print("-" * 45)
    for month, total, unique in results:
        print(f"{month} | {total:>15} | {unique:>15}")
    
    conn.close()

def main():
    """Main function to run all analyses"""
    print("=== Durian Export Rejection Analysis ===")
    print("Query: Top 10 exporters with highest rejection counts in past 12 months")
    print()
    
    # Main analysis
    top_10_exporters_by_rejections()
    
    # Additional context
    rejection_reasons_summary()
    monthly_rejection_trends()
    
    print("\n" + "="*80)
    print("Analysis complete!")

if __name__ == "__main__":
    main()
