import uuid
import random
import csv
from datetime import datetime, timedelta

# Configuration
TOTAL_RECORDS = 20000  # Configurable number of shipment records
PACKING_HOUSES_CSV = 'durian_packing_houses.csv'
OUTPUT_CSV = 'durian_export_shipments.csv'

# Shipment status distribution (E-trace dependent success rates)
# E-trace farms: 9% success rate
# Non-E-trace farms: 2% success rate
# Overall: ~7% success rate (70.5% farms use E-trace: 0.705*0.09 + 0.295*0.02 = 6.9%)
# Rejection: ~85% rate
# Pending: ~8% rate
STATUS_DISTRIBUTION = {
    'etrace_succeed': 0.09,     # 9% success rate for E-trace farms
    'non_etrace_succeed': 0.02, # 2% success rate for non-E-trace farms
    'rejected': 0.85,           # 85% rejection rate
    'pending': 0.08             # 8% pending rate (remaining percentage)
}

# General export rejection reasons in both English and Thai (limited to 5 total as requested)
# Ordered by expected frequency (most common first) with weighted distribution:
# 1. Incomplete documentation (35%) - Most common issue
# 2. Contains harmful chemicals (25%) - Second most common
# 3. Quality below standards (20%) - Third most common
# 4. Improper packaging (15%) - Fourth most common
# 5. Failed inspection (5%) - Least common
REJECT_REASONS = [
    {
        'english': 'Incomplete documentation',
        'thai': 'เอกสารไม่ครบถ้วน'
    },
    {
        'english': 'Contains harmful chemicals',
        'thai': 'พบสารเคมีที่เป็นอันตราย'
    },
    {
        'english': 'Quality below standards',
        'thai': 'คุณภาพต่ำกว่ามาตรฐาน'
    },
    {
        'english': 'Improper packaging',
        'thai': 'บรรจุภัณฑ์ไม่เหมาะสม'
    },
    {
        'english': 'Failed inspection',
        'thai': 'ตรวจสอบไม่ผ่าน'
    }
]

def load_packing_house_ids():
    """Load packing house IDs from the existing CSV file"""
    packing_house_ids = []
    try:
        with open(PACKING_HOUSES_CSV, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                packing_house_ids.append(row['id'])
        print(f"Loaded {len(packing_house_ids)} packing house IDs")
        return packing_house_ids
    except FileNotFoundError:
        print(f"Error: {PACKING_HOUSES_CSV} not found. Please run durian_packing_houses.py first.")
        return []

def load_farm_data():
    """Load farm data including IDs and e-trace status"""
    farms = []
    try:
        with open('durian_farms.csv', 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                farms.append({
                    'id': row['id'],
                    'uses_etrace': row['uses_etrace'].lower() == 'true'
                })
        print(f"Loaded {len(farms)} farms")
        return farms
    except FileNotFoundError:
        print(f"Error: durian_farms.csv not found. Please run durian_farms_generator.py first.")
        return []

def generate_packing_house_rejection_profiles(packing_house_ids):
    """Generate unique rejection reason profiles for each packing house"""
    profiles = {}

    # Define different rejection profile types
    profile_types = [
        # Documentation-heavy problems (poor paperwork management)
        [0.50, 0.20, 0.15, 0.10, 0.05],  # 50% documentation issues
        [0.45, 0.25, 0.15, 0.10, 0.05],  # 45% documentation issues

        # Chemical contamination problems (poor safety protocols)
        [0.15, 0.45, 0.20, 0.15, 0.05],  # 45% chemical issues
        [0.20, 0.40, 0.20, 0.15, 0.05],  # 40% chemical issues

        # Quality control problems (poor production standards)
        [0.20, 0.15, 0.45, 0.15, 0.05],  # 45% quality issues
        [0.25, 0.20, 0.35, 0.15, 0.05],  # 35% quality issues

        # Packaging problems (poor packaging processes)
        [0.20, 0.20, 0.20, 0.35, 0.05],  # 35% packaging issues
        [0.25, 0.25, 0.15, 0.30, 0.05],  # 30% packaging issues

        # Inspection failure problems (poor final checks)
        [0.25, 0.25, 0.25, 0.15, 0.10],  # 10% inspection failures
        [0.30, 0.20, 0.20, 0.20, 0.10],  # 10% inspection failures

        # Balanced profiles (multiple issues)
        [0.30, 0.25, 0.25, 0.15, 0.05],  # Balanced 1
        [0.35, 0.20, 0.20, 0.20, 0.05],  # Balanced 2
        [0.25, 0.30, 0.25, 0.15, 0.05],  # Balanced 3
    ]

    # Assign random profiles to each packing house
    for packing_house_id in packing_house_ids:
        profile = random.choice(profile_types)
        profiles[packing_house_id] = profile

    return profiles

def generate_timestamp_last_12_months():
    """Generate a random timestamp within the last 12 months"""
    now = datetime.now()
    twelve_months_ago = now - timedelta(days=365)

    # Generate random time between 12 months ago and now
    time_diff = now - twelve_months_ago
    random_seconds = random.randint(0, int(time_diff.total_seconds()))
    random_time = twelve_months_ago + timedelta(seconds=random_seconds)

    # Convert to Unix timestamp
    return int(random_time.timestamp())

def generate_shipment_weight(farm_uses_etrace, shipment_status):
    """
    Generate shipment weight based on farm e-trace status and shipment success

    Logic:
    - For successful shipments: E-trace farms generally have higher weights
    - But some non-E-trace farms can have higher weights than some E-trace farms
    - For rejected/pending: weights are more random regardless of e-trace status
    """
    if shipment_status == 'succeed':
        if farm_uses_etrace:
            # E-trace farms: higher base weight with some variation
            # 80% chance of high weight (800-1500 kg), 20% chance of medium weight (400-800 kg)
            if random.random() < 0.8:
                return random.randint(800, 1500)  # High weight range
            else:
                return random.randint(400, 800)   # Medium weight range
        else:
            # Non-E-trace farms: generally lower but some can be high
            # 30% chance of high weight (800-1200 kg), 70% chance of lower weight (200-700 kg)
            if random.random() < 0.3:
                return random.randint(800, 1200)  # Some non-E-trace farms can be high
            else:
                return random.randint(200, 700)   # Generally lower weight
    else:
        # For rejected/pending shipments: more random weights regardless of e-trace
        return random.randint(150, 1000)

def determine_shipment_status(packing_house_id, farm_uses_etrace, rejection_profiles):
    """
    Determine shipment status based on farm E-trace usage and packing house profiles

    Success rates:
    - E-trace farms: 9% success rate
    - Non-E-trace farms: 2% success rate (to maintain overall ~5% success rate)
    """
    rand = random.random()

    # Determine success probability based on E-trace usage
    if farm_uses_etrace:
        success_probability = 0.09  # 9% success rate for E-trace farms
    else:
        success_probability = 0.02  # 2% success rate for non-E-trace farms

    if rand < success_probability:
        return 'succeed', None, None
    elif rand < success_probability + 0.85:  # 85% rejection rate
        # Use packing house-specific rejection reason weights
        reason_weights = rejection_profiles.get(packing_house_id, [0.35, 0.25, 0.20, 0.15, 0.05])
        reason = random.choices(REJECT_REASONS, weights=reason_weights, k=1)[0]
        return 'rejected', reason['english'], reason['thai']
    else:
        return 'pending', None, None  # Remaining percentage for pending

def generate_shipments(packing_house_ids, farms, num_records):
    """Generate shipment records with farm_id and total_weight"""
    if not packing_house_ids or not farms:
        return []

    # Generate unique rejection profiles for each packing house
    rejection_profiles = generate_packing_house_rejection_profiles(packing_house_ids)

    shipments = []

    print(f"Generating {num_records} shipment records...")
    print(f"Generated {len(rejection_profiles)} unique rejection profiles for packing houses")
    print(f"Using {len(farms)} farms for shipment generation")

    for i in range(num_records):
        if i % 1000 == 0:
            print(f"Generated {i}/{num_records} records...")

        # Generate shipment ID
        shipment_id = str(uuid.uuid4())

        # Select random packing house and farm
        packing_house_id = random.choice(packing_house_ids)
        farm = random.choice(farms)
        farm_id = farm['id']
        farm_uses_etrace = farm['uses_etrace']

        # Determine status and reject reasons using packing house-specific profile and farm E-trace status
        status, reject_reason_en, reject_reason_th = determine_shipment_status(packing_house_id, farm_uses_etrace, rejection_profiles)

        # Generate weight based on farm e-trace status and shipment status
        total_weight = generate_shipment_weight(farm_uses_etrace, status)

        # Generate timestamp
        timestamp = generate_timestamp_last_12_months()

        shipment = {
            'shipment_id': shipment_id,
            'packing_house_id': packing_house_id,
            'farm_id': farm_id,
            'status': status,
            'reject_reason_english': reject_reason_en if reject_reason_en else '',
            'reject_reason_thai': reject_reason_th if reject_reason_th else '',
            'total_weight': total_weight,
            'timestamp': timestamp
        }

        shipments.append(shipment)

    return shipments, rejection_profiles

def save_shipments_to_csv(shipments, filename):
    """Save shipments to CSV file"""
    fieldnames = ['shipment_id', 'packing_house_id', 'farm_id', 'status', 'reject_reason_english', 'reject_reason_thai', 'total_weight', 'timestamp']

    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        for shipment in shipments:
            writer.writerow(shipment)

def print_statistics(shipments):
    """Print statistics about generated shipments"""
    total = len(shipments)
    succeed_count = sum(1 for s in shipments if s['status'] == 'succeed')
    rejected_count = sum(1 for s in shipments if s['status'] == 'rejected')
    pending_count = sum(1 for s in shipments if s['status'] == 'pending')

    print(f"\n=== Shipment Statistics ===")
    print(f"Total shipments: {total:,}")
    print(f"Successful: {succeed_count:,} ({succeed_count/total*100:.1f}%)")
    print(f"Rejected: {rejected_count:,} ({rejected_count/total*100:.1f}%)")
    print(f"Pending: {pending_count:,} ({pending_count/total*100:.1f}%)")

    # Rejection reason breakdown
    if rejected_count > 0:
        print(f"\n=== Rejection Reasons (English) - Weighted Distribution ===")
        reason_counts_en = {}
        for shipment in shipments:
            if shipment['status'] == 'rejected' and shipment['reject_reason_english']:
                reason = shipment['reject_reason_english']
                reason_counts_en[reason] = reason_counts_en.get(reason, 0) + 1

        # Sort by count descending to show the weighted distribution
        sorted_reasons = sorted(reason_counts_en.items(), key=lambda x: x[1], reverse=True)
        for reason, count in sorted_reasons:
            percentage = count/rejected_count*100
            print(f"- {reason}: {count:,} ({percentage:.1f}%)")

        print(f"\n=== Rejection Reasons (Thai) - Weighted Distribution ===")
        reason_counts_th = {}
        for shipment in shipments:
            if shipment['status'] == 'rejected' and shipment['reject_reason_thai']:
                reason = shipment['reject_reason_thai']
                reason_counts_th[reason] = reason_counts_th.get(reason, 0) + 1

        # Sort by count descending
        sorted_reasons_th = sorted(reason_counts_th.items(), key=lambda x: x[1], reverse=True)
        for reason, count in sorted_reasons_th:
            percentage = count/rejected_count*100
            print(f"- {reason}: {count:,} ({percentage:.1f}%)")

        print(f"\n=== Expected vs Actual Distribution ===")
        expected_weights = [0.35, 0.25, 0.20, 0.15, 0.05]
        reason_names = [r['english'] for r in REJECT_REASONS]
        print("Reason | Expected % | Actual %")
        print("-" * 50)
        for i, (reason, weight) in enumerate(zip(reason_names, expected_weights)):
            actual_count = reason_counts_en.get(reason, 0)
            actual_pct = actual_count/rejected_count*100 if rejected_count > 0 else 0
            print(f"{reason[:30]:<30} | {weight*100:>8.1f}% | {actual_pct:>7.1f}%")

def analyze_packing_house_profiles(rejection_profiles):
    """Analyze and display packing house rejection profiles"""
    print(f"\n=== Packing House Rejection Profiles Analysis ===")

    # Group packing houses by their dominant rejection reason
    profile_analysis = {}
    reason_names = [r['english'] for r in REJECT_REASONS]

    for packing_house_id, weights in rejection_profiles.items():
        # Find the dominant rejection reason (highest weight)
        max_weight_idx = weights.index(max(weights))
        dominant_reason = reason_names[max_weight_idx]
        dominant_percentage = weights[max_weight_idx] * 100

        if dominant_reason not in profile_analysis:
            profile_analysis[dominant_reason] = []

        profile_analysis[dominant_reason].append({
            'id': packing_house_id,
            'percentage': dominant_percentage,
            'weights': weights
        })

    # Display analysis
    for reason, houses in profile_analysis.items():
        print(f"\n{reason} - Dominant Issue ({len(houses)} packing houses):")
        for house in houses[:3]:  # Show top 3 examples
            print(f"  - House {house['id'][:8]}...: {house['percentage']:.1f}% {reason}")
            weights_str = " | ".join([f"{reason_names[i][:15]}: {w*100:.1f}%" for i, w in enumerate(house['weights'])])
            print(f"    Full profile: {weights_str}")

def analyze_weight_patterns(shipments, farms):
    """Analyze weight patterns by E-trace status and shipment success"""
    print(f"\n=== Weight Analysis by E-trace Status ===")

    # Create farm lookup for e-trace status
    farm_etrace_lookup = {farm['id']: farm['uses_etrace'] for farm in farms}

    # Categorize shipments by status and e-trace
    weight_data = {
        'succeed_etrace': [],
        'succeed_no_etrace': [],
        'rejected_etrace': [],
        'rejected_no_etrace': [],
        'pending_etrace': [],
        'pending_no_etrace': []
    }

    for shipment in shipments:
        farm_id = shipment['farm_id']
        uses_etrace = farm_etrace_lookup.get(farm_id, False)
        status = shipment['status']
        weight = shipment['total_weight']

        if status == 'succeed':
            if uses_etrace:
                weight_data['succeed_etrace'].append(weight)
            else:
                weight_data['succeed_no_etrace'].append(weight)
        elif status == 'rejected':
            if uses_etrace:
                weight_data['rejected_etrace'].append(weight)
            else:
                weight_data['rejected_no_etrace'].append(weight)
        else:  # pending
            if uses_etrace:
                weight_data['pending_etrace'].append(weight)
            else:
                weight_data['pending_no_etrace'].append(weight)

    # Calculate and display statistics
    print("Category | Count | Avg Weight | Min | Max | Median")
    print("-" * 60)

    for category, weights in weight_data.items():
        if weights:
            avg_weight = sum(weights) / len(weights)
            min_weight = min(weights)
            max_weight = max(weights)
            median_weight = sorted(weights)[len(weights)//2]

            print(f"{category:<20} | {len(weights):>5} | {avg_weight:>10.1f} | {min_weight:>3} | {max_weight:>3} | {median_weight:>6}")

    # Calculate success rates by E-trace status
    total_etrace_shipments = len(weight_data['succeed_etrace']) + len(weight_data['rejected_etrace']) + len(weight_data['pending_etrace'])
    total_no_etrace_shipments = len(weight_data['succeed_no_etrace']) + len(weight_data['rejected_no_etrace']) + len(weight_data['pending_no_etrace'])

    etrace_success_rate = (len(weight_data['succeed_etrace']) / total_etrace_shipments * 100) if total_etrace_shipments > 0 else 0
    no_etrace_success_rate = (len(weight_data['succeed_no_etrace']) / total_no_etrace_shipments * 100) if total_no_etrace_shipments > 0 else 0

    print(f"\n=== E-trace Success Rate Analysis ===")
    print(f"E-trace farms: {len(weight_data['succeed_etrace']):,} successes out of {total_etrace_shipments:,} shipments ({etrace_success_rate:.1f}%)")
    print(f"Non-E-trace farms: {len(weight_data['succeed_no_etrace']):,} successes out of {total_no_etrace_shipments:,} shipments ({no_etrace_success_rate:.1f}%)")

    if etrace_success_rate > no_etrace_success_rate:
        improvement = etrace_success_rate / no_etrace_success_rate if no_etrace_success_rate > 0 else float('inf')
        print(f"✅ E-trace advantage: {improvement:.1f}x higher success rate")
    else:
        print("❌ Unexpected: Non-E-trace farms have higher success rate")

    # Verify the logic: E-trace farms should generally have higher weights for successful shipments
    succeed_etrace_avg = sum(weight_data['succeed_etrace']) / len(weight_data['succeed_etrace']) if weight_data['succeed_etrace'] else 0
    succeed_no_etrace_avg = sum(weight_data['succeed_no_etrace']) / len(weight_data['succeed_no_etrace']) if weight_data['succeed_no_etrace'] else 0

    print(f"\n=== E-trace Weight Logic Verification ===")
    print(f"Successful shipments - E-trace farms avg: {succeed_etrace_avg:.1f} kg")
    print(f"Successful shipments - Non-E-trace farms avg: {succeed_no_etrace_avg:.1f} kg")

    if succeed_etrace_avg > succeed_no_etrace_avg:
        print("✅ Logic verified: E-trace farms have higher average weight for successful shipments")
    else:
        print("❌ Logic issue: Non-E-trace farms have higher average weight")

    # Check for exceptions (non-E-trace farms with high weights)
    high_weight_no_etrace = [w for w in weight_data['succeed_no_etrace'] if w > 800]
    low_weight_etrace = [w for w in weight_data['succeed_etrace'] if w < 800]

    print(f"Non-E-trace farms with high weights (>800kg): {len(high_weight_no_etrace)} cases")
    print(f"E-trace farms with lower weights (<800kg): {len(low_weight_etrace)} cases")
    print("✅ Some non-E-trace farms can outperform some E-trace farms (realistic variation)")

def main():
    print("=== Durian Export Shipments Generator ===")
    print("Each packing house will have unique rejection reason ratios")
    print("Including farm_id and total_weight with E-trace weight logic")
    print("E-trace farms: 9% success rate | Non-E-trace farms: 2% success rate")

    # Load packing house IDs
    packing_house_ids = load_packing_house_ids()
    if not packing_house_ids:
        return

    # Load farm data
    farms = load_farm_data()
    if not farms:
        return

    # Generate shipments with packing house-specific profiles and farm data
    shipments, rejection_profiles = generate_shipments(packing_house_ids, farms, TOTAL_RECORDS)

    if not shipments:
        print("No shipments generated.")
        return

    # Save to CSV
    save_shipments_to_csv(shipments, OUTPUT_CSV)
    print(f"\nShipments saved to {OUTPUT_CSV}")

    # Print statistics
    print_statistics(shipments)

    # Analyze packing house profiles
    analyze_packing_house_profiles(rejection_profiles)

    # Analyze weight patterns by E-trace and status
    analyze_weight_patterns(shipments, farms)

    # Show sample records
    print(f"\n=== Sample Records ===")
    for i, shipment in enumerate(shipments[:5]):
        print(f"Shipment {i+1}:")
        print(f"  ID: {shipment['shipment_id']}")
        print(f"  Packing House: {shipment['packing_house_id']}")
        print(f"  Farm ID: {shipment['farm_id']}")
        print(f"  Status: {shipment['status']}")
        print(f"  Total Weight: {shipment['total_weight']} kg")
        if shipment['reject_reason_english']:
            print(f"  Reject Reason (EN): {shipment['reject_reason_english']}")
        if shipment['reject_reason_thai']:
            print(f"  Reject Reason (TH): {shipment['reject_reason_thai']}")
        # Convert timestamp to readable format for display
        readable_time = datetime.fromtimestamp(shipment['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
        print(f"  Timestamp: {shipment['timestamp']} ({readable_time})")
        print("-" * 60)

if __name__ == "__main__":
    main()
