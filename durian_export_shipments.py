import uuid
import random
import csv
from datetime import datetime, timedelta

# Configuration
TOTAL_RECORDS = 20000  # Configurable number of shipment records
PACKING_HOUSES_CSV = 'durian_packing_houses.csv'
OUTPUT_CSV = 'durian_export_shipments.csv'

# Shipment status distribution (90% rejection rate as requested)
STATUS_DISTRIBUTION = {
    'succeed': 0.05,    # 5% success rate
    'rejected': 0.90,   # 90% rejection rate
    'pending': 0.05     # 5% pending
}

# General export rejection reasons in both English and Thai (limited to 5 total as requested)
REJECT_REASONS = [
    {
        'english': 'Incomplete documentation',
        'thai': 'เอกสารไม่ครบถ้วน'
    },
    {
        'english': 'Contains harmful chemicals',
        'thai': 'พบสารเคมีที่เป็นอันตราย'
    },
    {
        'english': 'Quality below standards',
        'thai': 'คุณภาพต่ำกว่ามาตรฐาน'
    },
    {
        'english': 'Improper packaging',
        'thai': 'บรรจุภัณฑ์ไม่เหมาะสม'
    },
    {
        'english': 'Failed inspection',
        'thai': 'ตรวจสอบไม่ผ่าน'
    }
]

def load_packing_house_ids():
    """Load packing house IDs from the existing CSV file"""
    packing_house_ids = []
    try:
        with open(PACKING_HOUSES_CSV, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                packing_house_ids.append(row['id'])
        print(f"Loaded {len(packing_house_ids)} packing house IDs")
        return packing_house_ids
    except FileNotFoundError:
        print(f"Error: {PACKING_HOUSES_CSV} not found. Please run durian_packing_houses.py first.")
        return []

def generate_timestamp_last_12_months():
    """Generate a random timestamp within the last 12 months"""
    now = datetime.now()
    twelve_months_ago = now - timedelta(days=365)

    # Generate random time between 12 months ago and now
    time_diff = now - twelve_months_ago
    random_seconds = random.randint(0, int(time_diff.total_seconds()))
    random_time = twelve_months_ago + timedelta(seconds=random_seconds)

    # Convert to Unix timestamp
    return int(random_time.timestamp())

def determine_shipment_status():
    """Determine shipment status based on distribution with 90% rejection rate"""
    rand = random.random()

    if rand < STATUS_DISTRIBUTION['succeed']:
        return 'succeed', None, None
    elif rand < STATUS_DISTRIBUTION['succeed'] + STATUS_DISTRIBUTION['rejected']:
        reason = random.choice(REJECT_REASONS)
        return 'rejected', reason['english'], reason['thai']
    else:
        return 'pending', None, None

def generate_shipments(packing_house_ids, num_records):
    """Generate shipment records"""
    if not packing_house_ids:
        return []

    shipments = []

    print(f"Generating {num_records} shipment records...")

    for i in range(num_records):
        if i % 1000 == 0:
            print(f"Generated {i}/{num_records} records...")

        # Generate shipment ID
        shipment_id = str(uuid.uuid4())

        # Select random packing house
        packing_house_id = random.choice(packing_house_ids)

        # Determine status and reject reasons
        status, reject_reason_en, reject_reason_th = determine_shipment_status()

        # Generate timestamp
        timestamp = generate_timestamp_last_12_months()

        shipment = {
            'shipment_id': shipment_id,
            'packing_house_id': packing_house_id,
            'status': status,
            'reject_reason_english': reject_reason_en if reject_reason_en else '',
            'reject_reason_thai': reject_reason_th if reject_reason_th else '',
            'timestamp': timestamp
        }

        shipments.append(shipment)

    return shipments

def save_shipments_to_csv(shipments, filename):
    """Save shipments to CSV file"""
    fieldnames = ['shipment_id', 'packing_house_id', 'status', 'reject_reason_english', 'reject_reason_thai', 'timestamp']

    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        for shipment in shipments:
            writer.writerow(shipment)

def print_statistics(shipments):
    """Print statistics about generated shipments"""
    total = len(shipments)
    succeed_count = sum(1 for s in shipments if s['status'] == 'succeed')
    rejected_count = sum(1 for s in shipments if s['status'] == 'rejected')
    pending_count = sum(1 for s in shipments if s['status'] == 'pending')

    print(f"\n=== Shipment Statistics ===")
    print(f"Total shipments: {total:,}")
    print(f"Successful: {succeed_count:,} ({succeed_count/total*100:.1f}%)")
    print(f"Rejected: {rejected_count:,} ({rejected_count/total*100:.1f}%)")
    print(f"Pending: {pending_count:,} ({pending_count/total*100:.1f}%)")

    # Rejection reason breakdown
    if rejected_count > 0:
        print(f"\n=== Rejection Reasons (English) ===")
        reason_counts_en = {}
        for shipment in shipments:
            if shipment['status'] == 'rejected' and shipment['reject_reason_english']:
                reason = shipment['reject_reason_english']
                reason_counts_en[reason] = reason_counts_en.get(reason, 0) + 1

        for reason, count in reason_counts_en.items():
            print(f"- {reason}: {count} ({count/rejected_count*100:.1f}%)")

        print(f"\n=== Rejection Reasons (Thai) ===")
        reason_counts_th = {}
        for shipment in shipments:
            if shipment['status'] == 'rejected' and shipment['reject_reason_thai']:
                reason = shipment['reject_reason_thai']
                reason_counts_th[reason] = reason_counts_th.get(reason, 0) + 1

        for reason, count in reason_counts_th.items():
            print(f"- {reason}: {count} ({count/rejected_count*100:.1f}%)")

def main():
    print("=== Durian Export Shipments Generator ===")

    # Load packing house IDs
    packing_house_ids = load_packing_house_ids()
    if not packing_house_ids:
        return

    # Generate shipments
    shipments = generate_shipments(packing_house_ids, TOTAL_RECORDS)

    if not shipments:
        print("No shipments generated.")
        return

    # Save to CSV
    save_shipments_to_csv(shipments, OUTPUT_CSV)
    print(f"\nShipments saved to {OUTPUT_CSV}")

    # Print statistics
    print_statistics(shipments)

    # Show sample records
    print(f"\n=== Sample Records ===")
    for i, shipment in enumerate(shipments[:5]):
        print(f"Shipment {i+1}:")
        print(f"  ID: {shipment['shipment_id']}")
        print(f"  Packing House: {shipment['packing_house_id']}")
        print(f"  Status: {shipment['status']}")
        if shipment['reject_reason_english']:
            print(f"  Reject Reason (EN): {shipment['reject_reason_english']}")
        if shipment['reject_reason_thai']:
            print(f"  Reject Reason (TH): {shipment['reject_reason_thai']}")
        # Convert timestamp to readable format for display
        readable_time = datetime.fromtimestamp(shipment['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
        print(f"  Timestamp: {shipment['timestamp']} ({readable_time})")
        print("-" * 50)

if __name__ == "__main__":
    main()
