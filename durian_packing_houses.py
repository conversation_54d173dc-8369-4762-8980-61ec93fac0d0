import uuid
import random
import csv

# Thai first and last names (with Thai script)
thai_first_names = ["สมชาย", "สมศรี", "สมนึก", "สมใจ", "สมพร", "สมหมาย", "สมบัติ",
                    "พรนิภา", "มานะ", "ชัยวัฒน์", "วิชิตา", "ปราณี", "สุวรรณ", "อนงค์"]
thai_last_names = ["สุขสันต์", "วงศ์ชัยวัฒน์", "แซ่ตั้ง", "เรืองไกร", "ทองคำ", "ทองดี",
                   "ไชยชนะ", "ชัยวัฒนตน์", "ยอดชัยวัฒน์", "ใจดี", "สุขใส", "ถาวรวงศ์"]

# Thai provinces known for durian (with Thai script)
provinces = ["ชุมพร", "ระยอง", "ตราด", "กาญจนบุรี", "ประจวบคีรีขันธ์", "นครศรีธรรมราช",
             "ยะลา", "ปัตตานี", "นราธิวาส", "ประจวบคีรีขันธ์"]

tambons = ["บ้านใหม่", "ท่าช้าง", "มะขาม", "วังน้ำเย็น", "ท่าตะเภา", "บางพระ", "หนองเบญจา", "คลองใหญ่"]

# Generate 50 packing houses
packing_houses = []
for i in range(50):
    # Generate UUID
    id = uuid.uuid4()

    # Generate owner name
    thai_first = random.choice(thai_first_names)
    thai_last = random.choice(thai_last_names)
    owner_thai = f"{thai_first} {thai_last}"

    # Generate packing house name
    name_thai = f"สวนทุเรียน{thai_first}"

    # Generate address
    province = random.choice(provinces)
    tambon = random.choice(tambons)
    address_thai = f"เลขที่ {random.randint(1, 999)} หมู่ที่ {random.randint(1, 12)}, ตำบล{tambon}, จังหวัด{province}"

    packing_houses.append({
        "id": id,
        "name_thai": name_thai,
        "owner_thai": owner_thai,
        "address_thai": address_thai
    })

# Save to CSV file
with open('durian_packing_houses.csv', 'w', newline='', encoding='utf-8') as csvfile:
    fieldnames = ['id', 'name_thai', 'owner_thai', 'address_thai']
    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

    writer.writeheader()
    for house in packing_houses:
        writer.writerow(house)

print(f"Generated 50 durian packing houses and saved to durian_packing_houses.csv")

# Print a few examples
print("\nSample entries:")
for house in packing_houses[:3]:
    print(f"ID: {house['id']}")
    print(f"Name: {house['name_thai']}")
    print(f"Owner: {house['owner_thai']}")
    print(f"Address: {house['address_thai']}")
    print("-" * 50)