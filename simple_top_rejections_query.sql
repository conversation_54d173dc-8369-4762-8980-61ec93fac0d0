-- Top 10 exporters with highest number of export rejections in the past 12 months, split by reject reasons

WITH exporter_rejections AS (
    SELECT 
        p.owner as exporter_name,
        p.name as packing_house_name,
        COUNT(*) as total_rejections,
        
        -- Split by rejection reasons
        SUM(CASE WHEN s.reject_reason_en = 'Quality below standards' THEN 1 ELSE 0 END) as quality_below_standards,
        SUM(CASE WHEN s.reject_reason_en = 'Improper packaging' THEN 1 ELSE 0 END) as improper_packaging,
        SUM(CASE WHEN s.reject_reason_en = 'Contains harmful chemicals' THEN 1 ELSE 0 END) as harmful_chemicals,
        SUM(CASE WHEN s.reject_reason_en = 'Incomplete documentation' THEN 1 ELSE 0 END) as incomplete_documentation,
        SUM(CASE WHEN s.reject_reason_en = 'Failed inspection' THEN 1 ELSE 0 END) as failed_inspection
        
    FROM packing_houses p
    JOIN export_shipments s ON p.id = s.packing_house_id
    WHERE s.status = 'rejected' 
    AND s.timestamp >= strftime('%s', 'now', '-12 months')  -- Past 12 months
    GROUP BY p.id, p.owner, p.name
    ORDER BY total_rejections DESC
    LIMIT 10
)
SELECT 
    ROW_NUMBER() OVER (ORDER BY total_rejections DESC) as rank,
    exporter_name,
    packing_house_name,
    total_rejections,
    quality_below_standards,
    improper_packaging,
    harmful_chemicals,
    incomplete_documentation,
    failed_inspection
FROM exporter_rejections;
