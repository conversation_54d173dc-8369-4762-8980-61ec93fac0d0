import sqlite3
from datetime import datetime

DATABASE_NAME = 'durian_export.db'

def connect_database():
    """Connect to the SQLite database"""
    try:
        conn = sqlite3.connect(DATABASE_NAME)
        return conn
    except sqlite3.Error as e:
        print(f"Error connecting to database: {e}")
        return None

def query_1_basic_stats(conn):
    """Query 1: Basic statistics"""
    print("=== Query 1: Basic Database Statistics ===")
    cursor = conn.cursor()
    
    # Total counts
    cursor.execute("SELECT COUNT(*) FROM packing_houses")
    packing_houses_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM export_shipments")
    shipments_count = cursor.fetchone()[0]
    
    print(f"Total packing houses: {packing_houses_count:,}")
    print(f"Total shipments: {shipments_count:,}")
    
    # Status breakdown
    cursor.execute("""
        SELECT status, COUNT(*) as count, 
               ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM export_shipments), 2) as percentage
        FROM export_shipments 
        GROUP BY status 
        ORDER BY count DESC
    """)
    
    print("\nShipment status breakdown:")
    for status, count, percentage in cursor.fetchall():
        print(f"  {status.capitalize()}: {count:,} ({percentage}%)")

def query_2_rejection_analysis(conn):
    """Query 2: Rejection reason analysis"""
    print("\n=== Query 2: Rejection Reason Analysis ===")
    cursor = conn.cursor()
    
    # English rejection reasons
    cursor.execute("""
        SELECT reject_reason_english, COUNT(*) as count,
               ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM export_shipments WHERE status = 'rejected'), 2) as percentage
        FROM export_shipments 
        WHERE status = 'rejected' AND reject_reason_english IS NOT NULL
        GROUP BY reject_reason_english 
        ORDER BY count DESC
    """)
    
    print("Rejection reasons (English):")
    for reason, count, percentage in cursor.fetchall():
        print(f"  {reason}: {count:,} ({percentage}%)")
    
    # Thai rejection reasons
    cursor.execute("""
        SELECT reject_reason_thai, COUNT(*) as count
        FROM export_shipments 
        WHERE status = 'rejected' AND reject_reason_thai IS NOT NULL
        GROUP BY reject_reason_thai 
        ORDER BY count DESC
        LIMIT 3
    """)
    
    print("\nTop 3 rejection reasons (Thai):")
    for reason, count in cursor.fetchall():
        print(f"  {reason}: {count:,}")

def query_3_packing_house_performance(conn):
    """Query 3: Packing house performance analysis"""
    print("\n=== Query 3: Packing House Performance ===")
    cursor = conn.cursor()
    
    cursor.execute("""
        SELECT p.owner_thai, p.name_thai,
               COUNT(s.shipment_id) as total_shipments,
               SUM(CASE WHEN s.status = 'succeed' THEN 1 ELSE 0 END) as successful,
               SUM(CASE WHEN s.status = 'rejected' THEN 1 ELSE 0 END) as rejected,
               SUM(CASE WHEN s.status = 'pending' THEN 1 ELSE 0 END) as pending,
               ROUND(SUM(CASE WHEN s.status = 'succeed' THEN 1 ELSE 0 END) * 100.0 / COUNT(s.shipment_id), 2) as success_rate
        FROM packing_houses p
        LEFT JOIN export_shipments s ON p.id = s.packing_house_id
        GROUP BY p.id, p.owner_thai, p.name_thai
        HAVING COUNT(s.shipment_id) > 0
        ORDER BY success_rate DESC, total_shipments DESC
        LIMIT 10
    """)
    
    print("Top 10 packing houses by success rate:")
    print("Owner | Packing House | Total | Success | Rejected | Pending | Success Rate")
    print("-" * 80)
    for owner, house, total, success, rejected, pending, rate in cursor.fetchall():
        print(f"{owner[:15]:<15} | {house[:20]:<20} | {total:>5} | {success:>7} | {rejected:>8} | {pending:>7} | {rate:>6}%")

def query_4_time_analysis(conn):
    """Query 4: Time-based analysis"""
    print("\n=== Query 4: Time-based Analysis ===")
    cursor = conn.cursor()
    
    # Monthly shipment trends
    cursor.execute("""
        SELECT strftime('%Y-%m', datetime(timestamp, 'unixepoch')) as month,
               COUNT(*) as total_shipments,
               SUM(CASE WHEN status = 'succeed' THEN 1 ELSE 0 END) as successful,
               SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected
        FROM export_shipments
        GROUP BY month
        ORDER BY month
        LIMIT 12
    """)
    
    print("Monthly shipment trends (first 12 months):")
    print("Month    | Total | Success | Rejected")
    print("-" * 35)
    for month, total, success, rejected in cursor.fetchall():
        print(f"{month} | {total:>5} | {success:>7} | {rejected:>8}")

def query_5_custom_search(conn):
    """Query 5: Custom search examples"""
    print("\n=== Query 5: Custom Search Examples ===")
    cursor = conn.cursor()
    
    # Find shipments with specific rejection reason
    cursor.execute("""
        SELECT p.owner_thai, p.name_thai, s.shipment_id, s.reject_reason_english,
               datetime(s.timestamp, 'unixepoch') as shipment_date
        FROM export_shipments s
        JOIN packing_houses p ON s.packing_house_id = p.id
        WHERE s.reject_reason_english = 'Contains harmful chemicals'
        ORDER BY s.timestamp DESC
        LIMIT 5
    """)
    
    print("Recent shipments rejected for 'Contains harmful chemicals':")
    for owner, house, shipment_id, reason, date in cursor.fetchall():
        print(f"  {owner} ({house}) - {shipment_id[:8]}... on {date}")
    
    # Find packing houses with high rejection rates
    cursor.execute("""
        SELECT p.owner_thai, p.name_thai,
               COUNT(s.shipment_id) as total,
               SUM(CASE WHEN s.status = 'rejected' THEN 1 ELSE 0 END) as rejected,
               ROUND(SUM(CASE WHEN s.status = 'rejected' THEN 1 ELSE 0 END) * 100.0 / COUNT(s.shipment_id), 1) as rejection_rate
        FROM packing_houses p
        JOIN export_shipments s ON p.id = s.packing_house_id
        GROUP BY p.id, p.owner_thai, p.name_thai
        HAVING COUNT(s.shipment_id) >= 300 AND rejection_rate > 95
        ORDER BY rejection_rate DESC
        LIMIT 5
    """)
    
    print(f"\nPacking houses with >95% rejection rate (min 300 shipments):")
    for owner, house, total, rejected, rate in cursor.fetchall():
        print(f"  {owner} ({house}): {rejected}/{total} rejected ({rate}%)")

def main():
    """Main function to run sample queries"""
    print("=== Durian Export Database Query Examples ===")
    
    conn = connect_database()
    if not conn:
        return
    
    try:
        # Run sample queries
        query_1_basic_stats(conn)
        query_2_rejection_analysis(conn)
        query_3_packing_house_performance(conn)
        query_4_time_analysis(conn)
        query_5_custom_search(conn)
        
        print(f"\n=== Database Schema ===")
        cursor = conn.cursor()
        
        # Show table schemas
        cursor.execute("SELECT sql FROM sqlite_master WHERE type='table'")
        for schema in cursor.fetchall():
            print(f"\n{schema[0]}")
        
        print(f"\n=== Available Tables ===")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        for table in cursor.fetchall():
            print(f"- {table[0]}")
            
    except sqlite3.Error as e:
        print(f"Database error: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    main()
