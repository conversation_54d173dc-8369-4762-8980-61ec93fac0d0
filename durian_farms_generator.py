import uuid
import random
import csv
from datetime import datetime

# Configuration
TOTAL_FARMS = 200
OUTPUT_CSV = 'durian_farms.csv'

# Thai farm names (prefix + suffix combinations)
farm_name_prefixes = [
    "สวน", "ไร่", "ฟาร์ม", "สวนผลไม้", "ไร่ทุเรียน", "สวนทุเรียน", 
    "เกษตรกรรม", "สวนเกษตร", "ไร่เกษตร", "สวนอินทรีย์"
]

farm_name_suffixes = [
    "ทองคำ", "สุขใส", "เจริญรุ่งเรือง", "ผลไม้หวาน", "ทุเรียนหอม", 
    "บ้านสวน", "ไผ่ทอง", "ลำธาร", "ป่าไผ่", "ดอนตาล",
    "หนองบัว", "คลองใส", "ทุ่งทอง", "ดอยสูง", "ลูกผึ้ง",
    "มะขาม", "ตะโก", "บางแสน", "หัวหิน", "เขาใหญ่"
]

# Thai owner names
thai_first_names = [
    "สมชาย", "สมศรี", "สมนึก", "สมใจ", "สมพร", "สมหมาย", "สมบัติ", 
    "พรนิภา", "มานะ", "ชัยวัฒน์", "วิชิตา", "ปราณี", "สุวรรณ", "อนงค์",
    "นิรันดร์", "วิไล", "สุรชัย", "ประยุทธ", "กมลชัย", "สุภาพ",
    "วิทยา", "สุนทร", "ประสิทธิ์", "กิตติ", "วีระ", "สุรพล"
]

thai_last_names = [
    "สุขสันต์", "วงศ์ชัยวัฒน์", "แซ่ตั้ง", "เรืองไกร", "ทองคำ", "ทองดี", 
    "ไชยชนะ", "ชัยวัฒนตน์", "ยอดชัยวัฒน์", "ใจดี", "สุขใส", "ถาวรวงศ์",
    "จันทร์เพ็ญ", "ศรีสุข", "บุญมี", "มีสุข", "รักษ์ดี", "พูลสวัสดิ์",
    "สวัสดิ์ชัย", "เจริญสุข", "ประเสริฐ", "มั่งมี", "รุ่งเรือง", "เจริญผล"
]

# Thai provinces known for durian farming
provinces = [
    "ชุมพร", "ระยอง", "ตราด", "กาญจนบุรี", "ประจวบคีรีขันธ์", 
    "นครศรีธรรมราช", "ยะลา", "ปัตตานี", "นราธิวาส", "สุราษฎร์ธานี",
    "กระบี่", "พังงา", "ภูเก็ต", "สงขลา", "สตูล"
]

# Thai tambons (sub-districts)
tambons = [
    "บ้านใหม่", "ท่าช้าง", "มะขาม", "วังน้ำเย็น", "ท่าตะเภา", "บางพระ", 
    "หนองเบญจา", "คลองใหญ่", "บ้านโป่ง", "หนองปลิง", "คลองท่อม", 
    "ท่าศาลา", "บางสะพาน", "หนองแก", "คลองขุด", "บ้านตาขุน",
    "หนองหิน", "ท่าช้าง", "บางกะปิ", "คลองลาน", "หนองบัว", "ดอนตาล"
]

def generate_farm_name():
    """Generate a Thai farm name"""
    prefix = random.choice(farm_name_prefixes)
    suffix = random.choice(farm_name_suffixes)
    return f"{prefix}{suffix}"

def generate_owner_name():
    """Generate a Thai owner name"""
    first_name = random.choice(thai_first_names)
    last_name = random.choice(thai_last_names)
    return f"{first_name} {last_name}"

def generate_address():
    """Generate a Thai address"""
    house_number = random.randint(1, 999)
    moo = random.randint(1, 15)
    tambon = random.choice(tambons)
    province = random.choice(provinces)
    
    return f"เลขที่ {house_number} หมู่ที่ {moo}, ตำบล{tambon}, จังหวัด{province}"

def generate_uses_etrace():
    """Generate uses_etrace boolean with 70% true, 30% false"""
    return random.random() < 0.7  # 70% chance of using e-trace

def generate_farms(num_farms):
    """Generate farm records"""
    farms = []
    
    print(f"Generating {num_farms} farm records...")
    
    for i in range(num_farms):
        if i % 50 == 0:
            print(f"Generated {i}/{num_farms} farms...")
        
        farm = {
            'id': str(uuid.uuid4()),
            'name': generate_farm_name(),
            'address': generate_address(),
            'uses_etrace': generate_uses_etrace(),
            'owner': generate_owner_name()
        }
        
        farms.append(farm)
    
    return farms

def save_farms_to_csv(farms, filename):
    """Save farms to CSV file"""
    fieldnames = ['id', 'name', 'address', 'uses_etrace', 'owner']
    
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for farm in farms:
            writer.writerow(farm)

def print_statistics(farms):
    """Print statistics about generated farms"""
    total = len(farms)
    etrace_users = sum(1 for f in farms if f['uses_etrace'])
    non_etrace_users = total - etrace_users
    
    print(f"\n=== Farm Statistics ===")
    print(f"Total farms: {total:,}")
    print(f"Using e-trace: {etrace_users:,} ({etrace_users/total*100:.1f}%)")
    print(f"Not using e-trace: {non_etrace_users:,} ({non_etrace_users/total*100:.1f}%)")
    
    # Province distribution
    province_counts = {}
    for farm in farms:
        # Extract province from address
        address = farm['address']
        if 'จังหวัด' in address:
            province = address.split('จังหวัด')[1]
            province_counts[province] = province_counts.get(province, 0) + 1
    
    print(f"\n=== Top 5 Provinces by Farm Count ===")
    sorted_provinces = sorted(province_counts.items(), key=lambda x: x[1], reverse=True)
    for province, count in sorted_provinces[:5]:
        print(f"- {province}: {count} farms")
    
    # Farm name analysis
    name_prefixes = {}
    for farm in farms:
        name = farm['name']
        for prefix in farm_name_prefixes:
            if name.startswith(prefix):
                name_prefixes[prefix] = name_prefixes.get(prefix, 0) + 1
                break
    
    print(f"\n=== Farm Name Prefixes ===")
    sorted_prefixes = sorted(name_prefixes.items(), key=lambda x: x[1], reverse=True)
    for prefix, count in sorted_prefixes[:5]:
        print(f"- {prefix}: {count} farms")

def main():
    """Main function to generate farms"""
    print("=== Durian Farms Generator ===")
    
    # Generate farms
    farms = generate_farms(TOTAL_FARMS)
    
    # Save to CSV
    save_farms_to_csv(farms, OUTPUT_CSV)
    print(f"\nFarms saved to {OUTPUT_CSV}")
    
    # Print statistics
    print_statistics(farms)
    
    # Show sample records
    print(f"\n=== Sample Farm Records ===")
    for i, farm in enumerate(farms[:5]):
        print(f"Farm {i+1}:")
        print(f"  ID: {farm['id']}")
        print(f"  Name: {farm['name']}")
        print(f"  Address: {farm['address']}")
        print(f"  Uses e-trace: {farm['uses_etrace']}")
        print(f"  Owner: {farm['owner']}")
        print("-" * 60)
    
    print(f"\n✅ Successfully generated {len(farms)} farms!")
    print(f"Data saved to: {OUTPUT_CSV}")

if __name__ == "__main__":
    main()
