-- Query: Top 10 exporters with highest number of export rejections 
-- in the past 12 months, split by reject reasons

-- Note: Replace the timestamp value (1716825600) with the actual timestamp 
-- for 12 months ago from your current date
-- Current example uses approximately May 27, 2024 as 12 months ago

WITH exporter_rejections AS (
    SELECT 
        p.id as packing_house_id,
        p.name as packing_house_name,
        p.owner as owner_name,
        COUNT(*) as total_rejections,
        
        -- Breakdown by rejection reasons
        SUM(CASE WHEN s.reject_reason_en = 'Quality below standards' THEN 1 ELSE 0 END) as quality_issues,
        SUM(CASE WHEN s.reject_reason_en = 'Improper packaging' THEN 1 ELSE 0 END) as packaging_issues,
        SUM(CASE WHEN s.reject_reason_en = 'Contains harmful chemicals' THEN 1 ELSE 0 END) as chemical_issues,
        SUM(CASE WHEN s.reject_reason_en = 'Incomplete documentation' THEN 1 ELSE 0 END) as documentation_issues,
        SUM(CASE WHEN s.reject_reason_en = 'Failed inspection' THEN 1 ELSE 0 END) as inspection_failures,
        
        -- Calculate percentages
        ROUND(SUM(CASE WHEN s.reject_reason_en = 'Quality below standards' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 1) as quality_pct,
        ROUND(SUM(CASE WHEN s.reject_reason_en = 'Improper packaging' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 1) as packaging_pct,
        ROUND(SUM(CASE WHEN s.reject_reason_en = 'Contains harmful chemicals' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 1) as chemical_pct,
        ROUND(SUM(CASE WHEN s.reject_reason_en = 'Incomplete documentation' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 1) as documentation_pct,
        ROUND(SUM(CASE WHEN s.reject_reason_en = 'Failed inspection' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 1) as inspection_pct
        
    FROM packing_houses p
    JOIN export_shipments s ON p.id = s.packing_house_id
    WHERE s.status = 'rejected' 
    AND s.timestamp >= 1716825600  -- Replace with actual timestamp for 12 months ago
    GROUP BY p.id, p.name, p.owner
    ORDER BY total_rejections DESC
    LIMIT 10
)
SELECT 
    ROW_NUMBER() OVER (ORDER BY total_rejections DESC) as rank,
    owner_name as exporter_owner,
    packing_house_name,
    total_rejections,
    quality_issues,
    packaging_issues,
    chemical_issues,
    documentation_issues,
    inspection_failures,
    quality_pct || '%' as quality_percentage,
    packaging_pct || '%' as packaging_percentage,
    chemical_pct || '%' as chemical_percentage,
    documentation_pct || '%' as documentation_percentage,
    inspection_pct || '%' as inspection_percentage
FROM exporter_rejections;

-- Alternative query: Pivot format showing rejection reasons as columns
-- This version shows the data in a more spreadsheet-friendly format

WITH rejection_pivot AS (
    SELECT 
        p.owner as exporter_owner,
        p.name as packing_house_name,
        s.reject_reason_en,
        COUNT(*) as rejection_count
    FROM packing_houses p
    JOIN export_shipments s ON p.id = s.packing_house_id
    WHERE s.status = 'rejected' 
    AND s.timestamp >= 1716825600  -- Replace with actual timestamp for 12 months ago
    GROUP BY p.id, p.owner, p.name, s.reject_reason_en
),
exporter_totals AS (
    SELECT 
        p.owner as exporter_owner,
        p.name as packing_house_name,
        COUNT(*) as total_rejections
    FROM packing_houses p
    JOIN export_shipments s ON p.id = s.packing_house_id
    WHERE s.status = 'rejected' 
    AND s.timestamp >= 1716825600  -- Replace with actual timestamp for 12 months ago
    GROUP BY p.id, p.owner, p.name
)
SELECT 
    ROW_NUMBER() OVER (ORDER BY et.total_rejections DESC) as rank,
    et.exporter_owner,
    et.packing_house_name,
    et.total_rejections,
    
    -- Pivot rejection reasons into columns
    COALESCE(MAX(CASE WHEN rp.reject_reason_en = 'Quality below standards' THEN rp.rejection_count END), 0) as quality_below_standards,
    COALESCE(MAX(CASE WHEN rp.reject_reason_en = 'Improper packaging' THEN rp.rejection_count END), 0) as improper_packaging,
    COALESCE(MAX(CASE WHEN rp.reject_reason_en = 'Contains harmful chemicals' THEN rp.rejection_count END), 0) as harmful_chemicals,
    COALESCE(MAX(CASE WHEN rp.reject_reason_en = 'Incomplete documentation' THEN rp.rejection_count END), 0) as incomplete_documentation,
    COALESCE(MAX(CASE WHEN rp.reject_reason_en = 'Failed inspection' THEN rp.rejection_count END), 0) as failed_inspection
    
FROM exporter_totals et
LEFT JOIN rejection_pivot rp ON et.exporter_owner = rp.exporter_owner 
    AND et.packing_house_name = rp.packing_house_name
GROUP BY et.exporter_owner, et.packing_house_name, et.total_rejections
ORDER BY et.total_rejections DESC
LIMIT 10;

-- Summary query: Overall rejection statistics for context
SELECT 
    'SUMMARY: Overall Rejection Statistics (Past 12 Months)' as analysis_type,
    reject_reason_en as rejection_reason,
    reject_reason_th as rejection_reason_thai,
    COUNT(*) as total_count,
    ROUND(COUNT(*) * 100.0 / (
        SELECT COUNT(*) 
        FROM export_shipments 
        WHERE status = 'rejected' 
        AND timestamp >= 1716825600
    ), 2) as percentage
FROM export_shipments
WHERE status = 'rejected' 
AND timestamp >= 1716825600  -- Replace with actual timestamp for 12 months ago
GROUP BY reject_reason_en, reject_reason_th
ORDER BY total_count DESC;

-- Time-based analysis: Monthly rejection trends
SELECT 
    'TRENDS: Monthly Rejection Patterns' as analysis_type,
    strftime('%Y-%m', datetime(timestamp, 'unixepoch')) as month,
    COUNT(*) as total_rejections,
    COUNT(DISTINCT packing_house_id) as unique_exporters_with_rejections,
    ROUND(AVG(CASE WHEN reject_reason_en = 'Quality below standards' THEN 1.0 ELSE 0.0 END) * 100, 1) as quality_issues_pct,
    ROUND(AVG(CASE WHEN reject_reason_en = 'Improper packaging' THEN 1.0 ELSE 0.0 END) * 100, 1) as packaging_issues_pct,
    ROUND(AVG(CASE WHEN reject_reason_en = 'Contains harmful chemicals' THEN 1.0 ELSE 0.0 END) * 100, 1) as chemical_issues_pct,
    ROUND(AVG(CASE WHEN reject_reason_en = 'Incomplete documentation' THEN 1.0 ELSE 0.0 END) * 100, 1) as documentation_issues_pct,
    ROUND(AVG(CASE WHEN reject_reason_en = 'Failed inspection' THEN 1.0 ELSE 0.0 END) * 100, 1) as inspection_failures_pct
FROM export_shipments
WHERE status = 'rejected' 
AND timestamp >= 1716825600  -- Replace with actual timestamp for 12 months ago
GROUP BY strftime('%Y-%m', datetime(timestamp, 'unixepoch'))
ORDER BY month;

-- How to calculate the timestamp for 12 months ago:
-- In most SQL environments, you can use:
-- UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 12 MONTH))  -- MySQL
-- EXTRACT(EPOCH FROM (NOW() - INTERVAL '12 months'))  -- PostgreSQL
-- strftime('%s', 'now', '-12 months')                 -- SQLite

-- For SQLite specifically, you can replace the hardcoded timestamp with:
-- strftime('%s', 'now', '-12 months')

-- Example of dynamic timestamp calculation for SQLite:
/*
WITH date_filter AS (
    SELECT CAST(strftime('%s', 'now', '-12 months') AS INTEGER) as twelve_months_ago
)
SELECT ... 
FROM export_shipments s, date_filter df
WHERE s.timestamp >= df.twelve_months_ago
*/
