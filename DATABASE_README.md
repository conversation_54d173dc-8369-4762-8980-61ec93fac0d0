# Durian Export Database

This SQLite database contains data for durian packing houses and their export shipments, designed for analysis of export rejection patterns and border control effectiveness.

## Database Files

- **`durian_export.db`** - Main SQLite database file (6.5MB)
- **`create_durian_database.py`** - <PERSON><PERSON>t to create and populate the database
- **`query_durian_database.py`** - Example queries and database usage

## Database Schema

### Table: `packing_houses`
Contains information about durian packing houses.

| Column | Type | Description |
|--------|------|-------------|
| `id` | TEXT PRIMARY KEY | Unique identifier (UUID) |
| `name_thai` | TEXT NOT NULL | Packing house name in Thai |
| `owner_thai` | TEXT NOT NULL | Owner name in Thai |
| `address_thai` | TEXT NOT NULL | Address in Thai |
| `created_at` | TIMESTAMP | Record creation timestamp |

### Table: `export_shipments`
Contains export shipment records with rejection information.

| Column | Type | Description |
|--------|------|-------------|
| `shipment_id` | TEXT PRIMARY KEY | Unique shipment identifier (UUID) |
| `packing_house_id` | TEXT NOT NULL | Foreign key to packing_houses.id |
| `status` | TEXT NOT NULL | Shipment status: 'succeed', 'rejected', 'pending' |
| `reject_reason_english` | TEXT | Rejection reason in English (if rejected) |
| `reject_reason_thai` | TEXT | Rejection reason in Thai (if rejected) |
| `timestamp` | INTEGER NOT NULL | Unix timestamp of shipment |
| `created_at` | TIMESTAMP | Record creation timestamp |

## Data Statistics

- **Packing Houses**: 50 records
- **Export Shipments**: 20,000 records
- **Time Range**: 12 months (May 2024 - May 2025)
- **Rejection Rate**: 90% (realistic for analysis scenarios)
- **Success Rate**: 4.9%
- **Pending Rate**: 5.1%

## Rejection Reasons

The database includes 5 general rejection reasons in both English and Thai:

1. **Quality below standards** / คุณภาพต่ำกว่ามาตรฐาน
2. **Improper packaging** / บรรจุภัณฑ์ไม่เหมาะสม
3. **Contains harmful chemicals** / พบสารเคมีที่เป็นอันตราย
4. **Incomplete documentation** / เอกสารไม่ครบถ้วน
5. **Failed inspection** / ตรวจสอบไม่ผ่าน

## Database Indexes

The following indexes are created for optimal query performance:

- `idx_shipments_packing_house` - On export_shipments.packing_house_id
- `idx_shipments_status` - On export_shipments.status
- `idx_shipments_timestamp` - On export_shipments.timestamp

## Usage Examples

### Python with sqlite3

```python
import sqlite3
from datetime import datetime

# Connect to database
conn = sqlite3.connect('durian_export.db')
cursor = conn.cursor()

# Example 1: Get rejection statistics
cursor.execute("""
    SELECT reject_reason_english, COUNT(*) as count
    FROM export_shipments 
    WHERE status = 'rejected'
    GROUP BY reject_reason_english
    ORDER BY count DESC
""")

for reason, count in cursor.fetchall():
    print(f"{reason}: {count}")

# Example 2: Find packing houses with high success rates
cursor.execute("""
    SELECT p.owner_thai, p.name_thai,
           COUNT(s.shipment_id) as total,
           SUM(CASE WHEN s.status = 'succeed' THEN 1 ELSE 0 END) as successful,
           ROUND(SUM(CASE WHEN s.status = 'succeed' THEN 1 ELSE 0 END) * 100.0 / COUNT(s.shipment_id), 2) as success_rate
    FROM packing_houses p
    JOIN export_shipments s ON p.id = s.packing_house_id
    GROUP BY p.id
    ORDER BY success_rate DESC
    LIMIT 10
""")

# Example 3: Time-based analysis
cursor.execute("""
    SELECT strftime('%Y-%m', datetime(timestamp, 'unixepoch')) as month,
           COUNT(*) as total_shipments,
           SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected
    FROM export_shipments
    GROUP BY month
    ORDER BY month
""")

conn.close()
```

### Command Line SQLite

```bash
# Open database
sqlite3 durian_export.db

# Basic queries
.tables
.schema packing_houses
.schema export_shipments

# Count records
SELECT COUNT(*) FROM packing_houses;
SELECT COUNT(*) FROM export_shipments;

# Status breakdown
SELECT status, COUNT(*) FROM export_shipments GROUP BY status;
```

## Common Queries

### 1. Rejection Analysis
```sql
-- Top rejection reasons
SELECT reject_reason_english, COUNT(*) as count,
       ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM export_shipments WHERE status = 'rejected'), 2) as percentage
FROM export_shipments 
WHERE status = 'rejected'
GROUP BY reject_reason_english
ORDER BY count DESC;
```

### 2. Packing House Performance
```sql
-- Packing houses with best success rates
SELECT p.owner_thai, p.name_thai,
       COUNT(s.shipment_id) as total_shipments,
       SUM(CASE WHEN s.status = 'succeed' THEN 1 ELSE 0 END) as successful,
       ROUND(SUM(CASE WHEN s.status = 'succeed' THEN 1 ELSE 0 END) * 100.0 / COUNT(s.shipment_id), 2) as success_rate
FROM packing_houses p
LEFT JOIN export_shipments s ON p.id = s.packing_house_id
GROUP BY p.id
HAVING COUNT(s.shipment_id) > 0
ORDER BY success_rate DESC;
```

### 3. Time Trends
```sql
-- Monthly shipment trends
SELECT strftime('%Y-%m', datetime(timestamp, 'unixepoch')) as month,
       COUNT(*) as total,
       SUM(CASE WHEN status = 'succeed' THEN 1 ELSE 0 END) as successful,
       SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected,
       SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending
FROM export_shipments
GROUP BY month
ORDER BY month;
```

## File Generation

To regenerate the database:

1. Ensure CSV files exist: `durian_packing_houses.csv` and `durian_export_shipments.csv`
2. Run: `python create_durian_database.py`
3. The script will create a fresh database with all data loaded

## Notes

- All Thai text uses UTF-8 encoding
- Unix timestamps can be converted using `datetime(timestamp, 'unixepoch')`
- Foreign key constraints are enabled
- The database is optimized for analytical queries with appropriate indexes
- Rejection reasons are only populated for shipments with status = 'rejected'
