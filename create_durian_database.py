import sqlite3
import csv
import os
from datetime import datetime

# Database configuration
DATABASE_NAME = 'durian_export.db'
PACKING_HOUSES_CSV = 'durian_packing_houses.csv'
SHIPMENTS_CSV = 'durian_export_shipments.csv'

def create_database():
    """Create SQLite database and tables"""
    # Remove existing database if it exists
    if os.path.exists(DATABASE_NAME):
        os.remove(DATABASE_NAME)
        print(f"Removed existing database: {DATABASE_NAME}")

    # Create new database connection
    conn = sqlite3.connect(DATABASE_NAME)
    cursor = conn.cursor()

    # Create packing_houses table
    cursor.execute('''
        CREATE TABLE packing_houses (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            owner TEXT NOT NULL,
            address TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Create export_shipments table
    cursor.execute('''
        CREATE TABLE export_shipments (
            shipment_id TEXT PRIMARY KEY,
            packing_house_id TEXT NOT NULL,
            status TEXT NOT NULL CHECK (status IN ('succeed', 'rejected', 'pending')),
            reject_reason_en TEXT,
            reject_reason_local TEXT,
            timestamp INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (packing_house_id) REFERENCES packing_houses (id)
        )
    ''')

    # Create indexes for better query performance
    cursor.execute('CREATE INDEX idx_shipments_packing_house ON export_shipments (packing_house_id)')
    cursor.execute('CREATE INDEX idx_shipments_status ON export_shipments (status)')
    cursor.execute('CREATE INDEX idx_shipments_timestamp ON export_shipments (timestamp)')

    conn.commit()
    print("Database tables created successfully!")
    return conn

def load_packing_houses(conn):
    """Load packing houses data from CSV"""
    cursor = conn.cursor()

    if not os.path.exists(PACKING_HOUSES_CSV):
        print(f"Error: {PACKING_HOUSES_CSV} not found!")
        return False

    with open(PACKING_HOUSES_CSV, 'r', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        packing_houses_data = []

        for row in reader:
            packing_houses_data.append((
                row['id'],
                row['name_thai'],
                row['owner_thai'],
                row['address_thai']
            ))

    # Insert data using executemany for better performance
    cursor.executemany('''
        INSERT INTO packing_houses
        (id, name, owner, address)
        VALUES (?, ?, ?, ?)
    ''', packing_houses_data)

    conn.commit()
    print(f"Loaded {len(packing_houses_data)} packing houses into database")
    return True

def load_shipments(conn):
    """Load shipments data from CSV"""
    cursor = conn.cursor()

    if not os.path.exists(SHIPMENTS_CSV):
        print(f"Error: {SHIPMENTS_CSV} not found!")
        return False

    with open(SHIPMENTS_CSV, 'r', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        shipments_data = []

        for row in reader:
            shipments_data.append((
                row['shipment_id'],
                row['packing_house_id'],
                row['status'],
                row['reject_reason_english'] if row['reject_reason_english'] else None,
                row['reject_reason_thai'] if row['reject_reason_thai'] else None,
                int(row['timestamp'])
            ))

    # Insert data in batches for better performance
    batch_size = 1000
    total_inserted = 0

    for i in range(0, len(shipments_data), batch_size):
        batch = shipments_data[i:i + batch_size]
        cursor.executemany('''
            INSERT INTO export_shipments
            (shipment_id, packing_house_id, status, reject_reason_en,
             reject_reason_local, timestamp)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', batch)
        total_inserted += len(batch)
        print(f"Inserted {total_inserted}/{len(shipments_data)} shipments...")

    conn.commit()
    print(f"Loaded {len(shipments_data)} shipments into database")
    return True

def verify_data(conn):
    """Verify the loaded data with some sample queries"""
    cursor = conn.cursor()

    print("\n=== Database Verification ===")

    # Count records in each table
    cursor.execute("SELECT COUNT(*) FROM packing_houses")
    packing_houses_count = cursor.fetchone()[0]
    print(f"Packing houses: {packing_houses_count:,}")

    cursor.execute("SELECT COUNT(*) FROM export_shipments")
    shipments_count = cursor.fetchone()[0]
    print(f"Export shipments: {shipments_count:,}")

    # Shipment status breakdown
    cursor.execute("""
        SELECT status, COUNT(*) as count,
               ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM export_shipments), 1) as percentage
        FROM export_shipments
        GROUP BY status
        ORDER BY count DESC
    """)

    print(f"\n=== Shipment Status Distribution ===")
    for status, count, percentage in cursor.fetchall():
        print(f"{status.capitalize()}: {count:,} ({percentage}%)")

    # Top rejection reasons
    cursor.execute("""
        SELECT reject_reason_en, COUNT(*) as count
        FROM export_shipments
        WHERE status = 'rejected' AND reject_reason_en IS NOT NULL
        GROUP BY reject_reason_en
        ORDER BY count DESC
    """)

    print(f"\n=== Top Rejection Reasons ===")
    for reason, count in cursor.fetchall():
        print(f"- {reason}: {count:,}")

    # Sample join query
    cursor.execute("""
        SELECT p.owner, p.name,
               COUNT(s.shipment_id) as total_shipments,
               SUM(CASE WHEN s.status = 'succeed' THEN 1 ELSE 0 END) as successful,
               SUM(CASE WHEN s.status = 'rejected' THEN 1 ELSE 0 END) as rejected
        FROM packing_houses p
        LEFT JOIN export_shipments s ON p.id = s.packing_house_id
        GROUP BY p.id, p.owner, p.name
        ORDER BY total_shipments DESC
        LIMIT 5
    """)

    print(f"\n=== Top 5 Packing Houses by Shipment Volume ===")
    for owner, house_name, total, successful, rejected in cursor.fetchall():
        success_rate = (successful / total * 100) if total > 0 else 0
        print(f"- {owner} ({house_name}): {total:,} shipments, {success_rate:.1f}% success rate")

    # Time range verification
    cursor.execute("SELECT MIN(timestamp), MAX(timestamp) FROM export_shipments")
    min_ts, max_ts = cursor.fetchone()
    min_date = datetime.fromtimestamp(min_ts).strftime('%Y-%m-%d')
    max_date = datetime.fromtimestamp(max_ts).strftime('%Y-%m-%d')
    days_range = (max_ts - min_ts) / (24 * 3600)

    print(f"\n=== Time Range ===")
    print(f"From: {min_date} to {max_date}")
    print(f"Range: {days_range:.1f} days")

def main():
    """Main function to create database and load data"""
    print("=== Durian Export Database Creator ===")

    # Create database and tables
    conn = create_database()

    try:
        # Load packing houses data
        if not load_packing_houses(conn):
            return

        # Load shipments data
        if not load_shipments(conn):
            return

        # Verify the loaded data
        verify_data(conn)

        print(f"\n✅ Database created successfully: {DATABASE_NAME}")
        print("You can now query the database using SQLite tools or Python sqlite3 module.")

    except Exception as e:
        print(f"Error: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    main()
